<?php
/**
 * Admin Lots Management View
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get filter parameters
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$auction_house_filter = isset($_GET['auction_house']) ? sanitize_text_field($_GET['auction_house']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';

// Build query args
$args = array(
    'limit' => 50,
    'search' => $search,
    'auction_house' => $auction_house_filter,
    'status' => $status_filter,
    'orderby' => 'closing_time',
    'order' => 'ASC'
);

// Get lots for display
$lots = USF_Database::get_lots($args);

// Get single auction page URL for view functionality
$single_auction_page_id = USF_Database::get_setting('single_auction_page', '');
$single_auction_url = !empty($single_auction_page_id) ? get_permalink($single_auction_page_id) : home_url();
?>

<div class="wrap">
    <h1>Manage Auction Lots</h1>
    
    <div class="usf-admin-message-container"></div>
    
    <div class="usf-lots-filters">
        <form method="get" class="usf-filter-form">
            <input type="hidden" name="page" value="usf-auctions-lots" />
            <input type="text" name="search" id="lots-search" placeholder="Search lots..." value="<?php echo esc_attr($search); ?>" />
            <select name="auction_house" id="auction-house-filter">
                <option value="">All Auction Houses</option>
                <?php
                $auction_houses_options = USF_Auction_House_Helper::get_auction_houses_for_dropdown();
                foreach ($auction_houses_options as $value => $label): ?>
                    <option value="<?php echo esc_attr($value); ?>" <?php selected($auction_house_filter, $value); ?>><?php echo esc_html($label); ?></option>
                <?php endforeach; ?>
            </select>
            <select name="status" id="status-filter">
                <option value="">All Statuses</option>
                <option value="active" <?php selected($status_filter, 'active'); ?>>Active</option>
                <option value="closed" <?php selected($status_filter, 'closed'); ?>>Closed</option>
                <option value="expired" <?php selected($status_filter, 'expired'); ?>>Expired</option>
            </select>
            <button type="submit" class="button">Filter</button>
            <a href="<?php echo admin_url('admin.php?page=usf-auctions-lots'); ?>" class="button">Clear</a>
        </form>
    </div>
    
    <div class="usf-lots-table-container">
        <table class="wp-list-table widefat fixed striped" id="usf-lots-table">
            <thead>
                <tr>
                    <th>Lot ID</th>
                    <th>Model</th>
                    <th>Grade</th>
                    <th>Units</th>
                    <th>Min Offer</th>
                    <th>Closing Time</th>
                    <th>Auction House</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($lots)): ?>
                    <?php foreach ($lots as $lot): ?>
                        <tr data-lot-id="<?php echo esc_attr($lot->lot_id); ?>">
                            <td><?php echo esc_html($lot->lot_id); ?></td>
                            <td class="editable-field" data-field="model"><?php echo esc_html($lot->model); ?></td>
                            <td class="editable-field" data-field="grade"><?php echo esc_html($lot->grade); ?></td>
                            <td class="editable-field" data-field="total_units"><?php echo esc_html($lot->total_units); ?></td>
                            <td class="editable-field editable-price" data-field="min_offer">$<?php echo number_format($lot->min_offer, 2); ?></td>
                            <td class="editable-field editable-datetime" data-field="closing_time"><?php echo esc_html(date('M j, Y g:i A', strtotime($lot->closing_time))); ?></td>
                            <td><?php echo esc_html($lot->auction_house); ?></td>
                            <td>
                                <select class="usf-lot-status-select" data-lot-id="<?php echo esc_attr($lot->lot_id); ?>" data-original-status="<?php echo esc_attr($lot->status); ?>">
                                    <option value="active" <?php selected($lot->status, 'active'); ?>>Active</option>
                                    <option value="closed" <?php selected($lot->status, 'closed'); ?>>Closed</option>
                                    <option value="expired" <?php selected($lot->status, 'expired'); ?>>Expired</option>
                                </select>
                            </td>
                            <td class="actions-column">
                                <button class="button button-small usf-view-lot" data-lot-id="<?php echo esc_attr($lot->lot_id); ?>" title="View Auction">
                                    <span class="dashicons dashicons-visibility"></span> View
                                </button>
                                <button class="button button-small usf-delete-lot" data-lot-id="<?php echo esc_attr($lot->lot_id); ?>" title="Delete Lot">
                                    <span class="dashicons dashicons-trash"></span> Delete
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="9">No auction lots found.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<style>
.usf-lots-filters {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.usf-lots-filters input,
.usf-lots-filters select {
    padding: 5px 10px;
}

.usf-filter-form {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.usf-lots-table-container {
    margin-top: 20px;
}

.usf-admin-message-container {
    margin: 10px 0;
}

.usf-admin-message {
    padding: 10px 15px;
    margin: 5px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.usf-admin-message.success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.usf-admin-message.error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.editable-field {
    cursor: pointer;
    position: relative;
    padding: 8px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.editable-field:hover {
    background-color: #f0f0f1;
}

.editable-field.editing {
    background-color: #fff;
    border: 1px solid #007cba;
}

.editable-field input {
    width: 100%;
    border: none;
    background: transparent;
    font-size: inherit;
    font-family: inherit;
    padding: 0;
    margin: 0;
}

.editable-field input:focus {
    outline: none;
}

.edit-controls {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1000;
}

.edit-controls button {
    margin-right: 5px;
    font-size: 11px;
    padding: 2px 8px;
}

.usf-lot-status-select {
    padding: 3px 8px;
    border-radius: 3px;
    border: 1px solid #ddd;
    background: white;
    font-size: 12px;
}

.actions-column {
    white-space: nowrap;
}

.actions-column .button {
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
    gap: 3px;
}

.actions-column .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.usf-view-lot {
    color: #0073aa;
}

.usf-delete-lot {
    color: #d63638;
}

.usf-view-lot:hover {
    color: #005a87;
}

.usf-delete-lot:hover {
    color: #b32d2e;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// Pass data to JavaScript
window.usfLotsData = {
    singleAuctionUrl: '<?php echo esc_js($single_auction_url); ?>',
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('usf_admin_action'); ?>'
};
</script>
